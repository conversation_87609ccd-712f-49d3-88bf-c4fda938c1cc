"""
Hume AI service for emotion detection and text-to-speech with streaming support.
"""
import asyncio
import base64
import logging
import time
from typing import Dict, List, Any, AsyncGenerator, Optional, Union
from dataclasses import dataclass
import os
from contextlib import asynccontextmanager
import json

from hume import AsyncHumeClient
from hume.expression_measurement.stream import Config
from hume.expression_measurement.stream.socket_client import StreamConnectOptions
from hume.expression_measurement.stream import StreamLanguage, StreamFace
from hume.tts import (
    PostedUtterance,
    PostedUtteranceVoiceWithName,
    PostedContextWithGenerationId,
    FormatPcm
)

from .error_recovery import error_recovery_manager

logger = logging.getLogger(__name__)


@dataclass
class EmotionScore:
    """Individual emotion score."""
    name: str
    score: float
    confidence: float = 1.0


@dataclass
class EmotionAnalysisResult:
    """Result from emotion analysis."""
    primary_emotion: str
    emotion_intensity: float
    emotion_valence: float  # -1.0 (negative) to 1.0 (positive)
    emotion_arousal: float  # 0.0 (calm) to 1.0 (excited)
    emotions: List[EmotionScore]
    confidence_score: float
    processing_time_ms: float
    source: str  # 'audio', 'text', or 'combined'
    raw_data: Dict[str, Any]


@dataclass
class TTSChunk:
    """TTS audio chunk."""
    audio_data: bytes  # Raw audio bytes
    chunk_index: int
    is_final: bool
    timestamp_ms: float
    generation_id: Optional[str] = None


@dataclass
class TTSResult:
    """Complete TTS result."""
    audio_data: bytes
    generation_id: str
    processing_time_ms: float
    voice_settings: Dict[str, Any]
    total_chunks: int


class HumeEmotionClient:
    """
    Hume AI client for real-time emotion detection from audio and text.
    
    Features:
    - WebSocket streaming emotion measurement
    - Audio and text emotion analysis
    - Confidence scoring and quality assessment
    - Performance optimization for real-time use
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        timeout: float = 30.0
    ):
        self.api_key = api_key or os.getenv('HUME_API_KEY')
        if not self.api_key:
            raise ValueError("HUME_API_KEY must be provided or set as environment variable")
        
        self.timeout = timeout

        # Configure SSL settings for macOS compatibility
        import ssl
        import httpx

        # Create SSL context that works on macOS
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # Create HTTP client with SSL configuration
        http_client = httpx.AsyncClient(
            verify=False,  # Disable SSL verification for development
            timeout=self.timeout
        )

        self.client = AsyncHumeClient(
            api_key=self.api_key,
            httpx_client=http_client
        )
        
        # Performance tracking
        self.request_count = 0
        self.total_processing_time = 0.0
        
        logger.info("Initialized HumeEmotionClient")
    
    async def analyze_audio_stream(
        self,
        audio_data: bytes,
        sample_rate: int = 16000,
        include_prosody: bool = True,
        include_language: bool = False
    ) -> EmotionAnalysisResult:
        """
        Analyze emotions from audio data using streaming WebSocket.
        
        Args:
            audio_data: Raw audio bytes
            sample_rate: Audio sample rate
            include_prosody: Include speech prosody analysis
            include_language: Include language emotion analysis
            
        Returns:
            EmotionAnalysisResult with detected emotions
        """
        start_time = time.time()
        
        # Check circuit breaker
        if not error_recovery_manager.circuit_breakers['hume'].allow_request():
            # Use fallback - return neutral emotions
            logger.warning("Hume circuit breaker open, using neutral emotions fallback")
            return EmotionAnalysisResult(
                primary_emotion='neutral',
                emotion_intensity=0.5,
                emotion_valence=0.0,
                emotion_arousal=0.0,
                emotions=[EmotionScore(name='neutral', score=1.0, confidence=0.5)],
                confidence_score=0.5,
                processing_time_ms=1.0,
                source='fallback',
                raw_data={'fallback': True}
            )
        
        # Prepare context for error recovery
        context = {
            'operation': 'emotion_detection',
            'audio_data': audio_data,
            'sample_rate': sample_rate
        }
        
        try:
            # Use retry mechanism
            async def _analyze_audio():
                return await self._perform_audio_analysis(
                    audio_data, sample_rate, include_prosody, include_language, start_time
                )
            
            return await error_recovery_manager.retry_with_backoff(
                _analyze_audio, service='hume'
            )
            
        except Exception as e:
            logger.error(f"Hume audio analysis error: {e}")
            
            # Handle failure through error recovery manager
            recovery_result = await error_recovery_manager.handle_api_failure(
                'hume', e, context
            )
            
            if recovery_result['success'] and 'fallback_data' in recovery_result:
                # Return fallback emotions
                fallback_data = recovery_result['fallback_data']
                return EmotionAnalysisResult(
                    primary_emotion=fallback_data.get('primary_emotion', 'neutral'),
                    emotion_intensity=fallback_data.get('emotion_intensity', 0.5),
                    emotion_valence=fallback_data.get('emotion_valence', 0.0),
                    emotion_arousal=fallback_data.get('emotion_arousal', 0.0),
                    emotions=[EmotionScore(name='neutral', score=1.0)],
                    confidence_score=fallback_data.get('confidence_score', 0.5),
                    processing_time_ms=(time.time() - start_time) * 1000,
                    source='fallback',
                    raw_data={'fallback': True, 'error': str(e), 'fallback_data': fallback_data}
                )
            else:
                # Re-raise the original error
                raise
    
    async def _perform_audio_analysis(
        self,
        audio_data: bytes,
        sample_rate: int,
        include_prosody: bool,
        include_language: bool,
        start_time: float
    ) -> EmotionAnalysisResult:
        """Perform the actual audio analysis."""
        try:
            # Configure models to use
            config_models = {}
            if include_prosody:
                config_models['prosody'] = {}  # Use empty dict for prosody config
            if include_language:
                config_models['language'] = StreamLanguage()
            
            if not config_models:
                config_models['prosody'] = {}  # Default to prosody
            
            model_config = Config(**config_models)
            stream_options = StreamConnectOptions(config=model_config)
            
            # Encode audio data to base64
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')
            
            # Use raw WebSocket connection for audio data (following working examples)
            import websockets
            import ssl

            # Create SSL context
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE

            # Connect using raw WebSocket with API key in URL
            ws_url = f"wss://api.hume.ai/v0/stream/models?apiKey={self.api_key}"

            async with websockets.connect(
                ws_url,
                ssl=ssl_context,
                ping_interval=None
            ) as websocket:
                # Send audio data using JSON message format as per Hume documentation
                audio_message = {
                    "models": config_models,
                    "data": audio_base64
                }

                # Send the JSON message
                import json
                await websocket.send(json.dumps(audio_message))

                # Receive the response with timeout
                response_text = await asyncio.wait_for(
                    websocket.recv(),
                    timeout=5.0
                )

                # Parse the JSON response
                result = json.loads(response_text)
                
                # Process results
                processing_time = (time.time() - start_time) * 1000
                
                # Extract emotion data
                emotions = []
                primary_emotion = "neutral"
                emotion_intensity = 0.0
                confidence_score = 0.0
                raw_data = result
                
                if 'prosody' in result and result['prosody']:
                    prosody_data = result['prosody']
                    if 'predictions' in prosody_data and prosody_data['predictions']:
                        prediction = prosody_data['predictions'][0]
                        if 'emotions' in prediction:
                            for emotion in prediction['emotions']:
                                emotions.append(EmotionScore(
                                    name=emotion['name'],
                                    score=emotion['score'],
                                    confidence=1.0  # Hume doesn't provide per-emotion confidence
                                ))
                            
                            # Find primary emotion (highest score)
                            if emotions:
                                primary_emotion_obj = max(emotions, key=lambda x: x.score)
                                primary_emotion = primary_emotion_obj.name
                                emotion_intensity = primary_emotion_obj.score
                            
                            confidence_score = 0.8  # Default confidence for prosody
                
                if 'language' in result and result['language']:
                    language_data = result['language']
                    if 'predictions' in language_data and language_data['predictions']:
                        # Process language emotions similarly
                        for prediction in language_data['predictions']:
                            if 'emotions' in prediction:
                                for emotion in prediction['emotions']:
                                    emotions.append(EmotionScore(
                                        name=emotion['name'],
                                        score=emotion['score'],
                                        confidence=1.0
                                    ))

                
                # Calculate valence and arousal
                emotion_valence, emotion_arousal = self._calculate_valence_arousal(emotions)
                
                # Update performance metrics
                self._update_performance_metrics(processing_time)
                
                # Record success in error recovery manager
                error_recovery_manager.circuit_breakers['hume'].record_success()
                
                return EmotionAnalysisResult(
                    primary_emotion=primary_emotion,
                    emotion_intensity=emotion_intensity,
                    emotion_valence=emotion_valence,
                    emotion_arousal=emotion_arousal,
                    emotions=emotions,
                    confidence_score=confidence_score,
                    processing_time_ms=processing_time,
                    source='audio',
                    raw_data=raw_data
                )
                
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            logger.error(f"Error analyzing audio emotions: {e}")
            
            # Record failure in error recovery manager
            error_recovery_manager.circuit_breakers['hume'].record_failure()
            
            # Re-raise to be handled by the calling method
            raise
    
    async def analyze_text_emotions(
        self,
        text: str
    ) -> EmotionAnalysisResult:
        """
        Analyze emotions from text using language model.
        
        Args:
            text: Text to analyze
            
        Returns:
            EmotionAnalysisResult with detected emotions
        """
        start_time = time.time()
        
        try:
            # Configure language model
            model_config = Config(language=StreamLanguage())
            stream_options = StreamConnectOptions(config=model_config)
            
            # Connect to streaming endpoint
            async with self.client.expression_measurement.stream.connect(
                options=stream_options
            ) as socket:
                # Send text data
                result = await socket.send_text(text)
                
                # Process results
                processing_time = (time.time() - start_time) * 1000
                
                emotions = []
                primary_emotion = "neutral"
                emotion_intensity = 0.0
                confidence_score = 0.0
                raw_data = {}
                
                if hasattr(result, 'language') and result.language:
                    language_data = result.language
                    if language_data.predictions:
                        for prediction in language_data.predictions:
                            if hasattr(prediction, 'emotions'):
                                for emotion in prediction.emotions:
                                    emotions.append(EmotionScore(
                                        name=emotion.name,
                                        score=emotion.score,
                                        confidence=1.0
                                    ))
                        
                        # Find primary emotion
                        if emotions:
                            primary_emotion_obj = max(emotions, key=lambda x: x.score)
                            primary_emotion = primary_emotion_obj.name
                            emotion_intensity = primary_emotion_obj.score
                        
                        confidence_score = 0.7  # Default confidence for text
                        raw_data['language'] = {
                            'predictions': [
                                {
                                    'text': text,
                                    'emotions': [
                                        {'name': e.name, 'score': e.score}
                                        for e in emotions
                                    ]
                                }
                            ]
                        }
                
                # Calculate valence and arousal
                emotion_valence, emotion_arousal = self._calculate_valence_arousal(emotions)
                
                # Update performance metrics
                self._update_performance_metrics(processing_time)
                
                return EmotionAnalysisResult(
                    primary_emotion=primary_emotion,
                    emotion_intensity=emotion_intensity,
                    emotion_valence=emotion_valence,
                    emotion_arousal=emotion_arousal,
                    emotions=emotions,
                    confidence_score=confidence_score,
                    processing_time_ms=processing_time,
                    source='text',
                    raw_data=raw_data
                )
                
        except Exception as e:
            processing_time = (time.time() - start_time) * 1000
            logger.error(f"Error analyzing text emotions: {e}")
            
            # Return neutral result on error
            return EmotionAnalysisResult(
                primary_emotion="neutral",
                emotion_intensity=0.0,
                emotion_valence=0.0,
                emotion_arousal=0.0,
                emotions=[EmotionScore("neutral", 0.5)],
                confidence_score=0.0,
                processing_time_ms=processing_time,
                source='text',
                raw_data={'error': str(e)}
            )
    
    def _calculate_valence_arousal(self, emotions: List[EmotionScore]) -> tuple[float, float]:
        """
        Calculate emotion valence and arousal from emotion scores.
        
        Args:
            emotions: List of emotion scores
            
        Returns:
            Tuple of (valence, arousal) values
        """
        if not emotions:
            return 0.0, 0.0
        
        # Emotion mappings to valence/arousal space
        emotion_mappings = {
            # High valence (positive)
            'joy': (0.8, 0.7),
            'happiness': (0.8, 0.6),
            'excitement': (0.7, 0.9),
            'love': (0.9, 0.6),
            'contentment': (0.6, 0.3),
            'amusement': (0.7, 0.5),
            'admiration': (0.6, 0.4),
            'triumph': (0.8, 0.8),
            
            # Low valence (negative)
            'sadness': (-0.7, 0.3),
            'anger': (-0.6, 0.8),
            'fear': (-0.8, 0.8),
            'disgust': (-0.7, 0.5),
            'contempt': (-0.6, 0.4),
            'shame': (-0.6, 0.6),
            'guilt': (-0.5, 0.5),
            'disappointment': (-0.5, 0.4),
            
            # Neutral
            'neutral': (0.0, 0.2),
            'calm': (0.2, 0.1),
            'surprise': (0.0, 0.8),
            'confusion': (-0.2, 0.6),
        }
        
        total_valence = 0.0
        total_arousal = 0.0
        total_weight = 0.0
        
        for emotion in emotions:
            emotion_name = emotion.name.lower()
            if emotion_name in emotion_mappings:
                valence, arousal = emotion_mappings[emotion_name]
                weight = emotion.score
                
                total_valence += valence * weight
                total_arousal += arousal * weight
                total_weight += weight
        
        if total_weight > 0:
            return total_valence / total_weight, total_arousal / total_weight
        else:
            return 0.0, 0.0
    
    def _update_performance_metrics(self, processing_time_ms: float):
        """Update internal performance metrics."""
        self.request_count += 1
        self.total_processing_time += processing_time_ms
    
    def get_performance_stats(self) -> Dict[str, float]:
        """Get performance statistics."""
        if self.request_count == 0:
            return {
                'average_processing_time_ms': 0.0,
                'total_requests': 0
            }
        
        return {
            'average_processing_time_ms': self.total_processing_time / self.request_count,
            'total_requests': self.request_count
        }


class HumeTTSClient:
    """
    Hume AI client for streaming text-to-speech with emotion modulation.
    
    Features:
    - Streaming TTS synthesis
    - Emotion-aware voice modulation
    - Acting instructions support
    - Voice continuity across utterances
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        timeout: float = 30.0
    ):
        self.api_key = api_key or os.getenv('HUME_API_KEY')
        if not self.api_key:
            raise ValueError("HUME_API_KEY must be provided or set as environment variable")
        
        self.timeout = timeout

        # Configure SSL settings for macOS compatibility
        import ssl
        import httpx

        # Create HTTP client with SSL configuration
        http_client = httpx.AsyncClient(
            verify=False,  # Disable SSL verification for development
            timeout=self.timeout
        )

        self.client = AsyncHumeClient(
            api_key=self.api_key,
            httpx_client=http_client
        )
        
        # Performance tracking
        self.request_count = 0
        self.total_processing_time = 0.0
        self.total_first_chunk_time = 0.0
        
        logger.info("Initialized HumeTTSClient")
    
    async def synthesize_streaming(
        self,
        text: str,
        voice_name: Optional[str] = None,
        emotion_context: Optional[EmotionAnalysisResult] = None,
        acting_instructions: Optional[str] = None,
        speed: float = 1.0,
        context_generation_id: Optional[str] = None
    ) -> AsyncGenerator[TTSChunk, None]:
        """
        Stream TTS synthesis with emotion modulation.
        
        Args:
            text: Text to synthesize
            voice_name: Voice to use (optional)
            emotion_context: Emotion context for voice modulation
            acting_instructions: Acting instructions for voice
            speed: Speech speed (0.25 to 4.0)
            context_generation_id: Previous generation ID for continuity
            
        Yields:
            TTSChunk: Audio chunks as they're generated
        """
        start_time = time.time()
        first_chunk_time = None
        chunk_index = 0
        
        # Check circuit breaker
        circuit_breaker = error_recovery_manager.circuit_breakers['hume']
        if not circuit_breaker.allow_request():
            # Try to reset the circuit breaker once before giving up
            logger.warning(f"Hume circuit breaker open (state: {circuit_breaker.state}, failures: {circuit_breaker.failure_count}), attempting reset...")

            try:
                error_recovery_manager.reset_circuit_breaker('hume')
                logger.info("🔄 Hume circuit breaker reset successfully, retrying TTS")

                # Check if we can proceed after reset
                if not circuit_breaker.allow_request():
                    logger.warning("Circuit breaker still not allowing requests after reset, using text-only fallback")
                else:
                    logger.info("✅ Circuit breaker reset successful, proceeding with TTS")
            except Exception as reset_error:
                logger.error(f"Failed to reset circuit breaker: {reset_error}")

            # If still not allowed after reset attempt, use fallback
            if not circuit_breaker.allow_request():
                logger.warning("Using text-only fallback after circuit breaker reset attempt")
            yield TTSChunk(
                audio_data=b'',
                chunk_index=0,
                is_final=True,
                timestamp_ms=time.time() * 1000,
                generation_id=None
            )
            return
        
        # Prepare context for error recovery
        context = {
            'operation': 'tts',
            'text': text,
            'voice_name': voice_name
        }
        
        try:
            # Check circuit breaker before proceeding
            if not error_recovery_manager.circuit_breakers['hume'].allow_request():
                logger.warning("Hume circuit breaker open, using fallback")
                yield TTSChunk(
                    audio_data=b'',
                    chunk_index=0,
                    is_final=True,
                    timestamp_ms=time.time() * 1000,
                    generation_id=None
                )
                return

            # Perform TTS synthesis directly (error recovery handled internally)
            logger.info(f"🎵 About to call _perform_tts_synthesis")
            async for chunk in self._perform_tts_synthesis(
                text, voice_name, emotion_context, acting_instructions,
                speed, context_generation_id, start_time
            ):
                logger.info(f"🎵 Yielding chunk from _perform_tts_synthesis: chunk_index={chunk.chunk_index}, audio_bytes={len(chunk.audio_data)}, is_final={chunk.is_final}")
                yield chunk
            logger.info(f"🎵 Finished _perform_tts_synthesis iteration")

        except Exception as e:
            logger.error(f"Hume TTS error: {e}")

            # Handle failure through error recovery manager
            recovery_result = await error_recovery_manager.handle_api_failure(
                'hume', e, context
            )

            # Yield empty final chunk to indicate completion
            yield TTSChunk(
                audio_data=b'',
                chunk_index=0,
                is_final=True,
                timestamp_ms=time.time() * 1000,
                generation_id=None
            )
            
            if recovery_result['success'] and recovery_result['fallback_used']:
                # Return text-only fallback
                logger.info("Using text-only fallback for TTS")
                yield TTSChunk(
                    audio_data=b'',
                    chunk_index=0,
                    is_final=True,
                    timestamp_ms=time.time() * 1000,
                    generation_id=None
                )
            else:
                # Re-raise the original error
                raise
    
    async def _perform_tts_synthesis(
        self,
        text: str,
        voice_name: Optional[str],
        emotion_context: Optional[EmotionAnalysisResult],
        acting_instructions: Optional[str],
        speed: float,
        context_generation_id: Optional[str],
        start_time: float
    ) -> AsyncGenerator[TTSChunk, None]:
        """Perform the actual TTS synthesis."""
        logger.info(f"🎵 _perform_tts_synthesis called with text: '{text[:50]}...'")
        first_chunk_time = None
        chunk_index = 0
        
        try:
            # Build utterance with emotion-aware settings
            utterance_params = {
                'text': text,
                'speed': speed
            }
            
            # Add voice if specified
            if voice_name:
                utterance_params['voice'] = PostedUtteranceVoiceWithName(name=voice_name)
            
            # Build acting instructions from emotion context
            if emotion_context or acting_instructions:
                description_parts = []
                
                if emotion_context:
                    # Convert emotion context to acting instructions
                    emotion_description = self._emotion_to_acting_instructions(emotion_context)
                    if emotion_description:
                        description_parts.append(emotion_description)
                
                if acting_instructions:
                    description_parts.append(acting_instructions)
                
                if description_parts:
                    utterance_params['description'] = ', '.join(description_parts)
            
            utterance = PostedUtterance(**utterance_params)
            
            # Build request parameters for streaming TTS (simplified based on Hume docs)
            request_params = {
                'utterances': [utterance],
                'format': FormatPcm(type="pcm")  # Use PCM for streaming
            }

            # Add optional parameters only if needed
            # Note: instant_mode, num_generations, strip_headers might not be supported in streaming
            
            # Add context if provided
            if context_generation_id:
                request_params['context'] = PostedContextWithGenerationId(
                    generation_id=context_generation_id
                )
            
            # Stream synthesis
            generation_id = None
            logger.info(f"🎵 Starting Hume TTS synthesis with params: {request_params}")

            logger.info(f"🎵 About to iterate over synthesize_json_streaming...")
            logger.info(f"🎵 Available TTS methods: {dir(self.client.tts)}")

            # Try with minimal parameters first
            minimal_params = {
                'utterances': [PostedUtterance(text=text)],
                'format': FormatPcm(type="pcm")
            }
            logger.info(f"🎵 Trying minimal params: {minimal_params}")

            try:
                snippet_count = 0
                async for snippet in self.client.tts.synthesize_json_streaming(**minimal_params):
                    snippet_count += 1
                    logger.info(f"🎵 Received snippet #{snippet_count} from Hume API! Type: {type(snippet)}")
                    logger.info(f"🎵 Snippet attributes: {dir(snippet)}")

                    current_time = time.time()

                    # Record first chunk time
                    if first_chunk_time is None:
                        first_chunk_time = (current_time - start_time) * 1000
                        logger.info(f"🎵 First TTS chunk received in {first_chunk_time:.2f}ms")

                    # According to Hume docs, audio data is in snippet.audio and is base64 encoded
                    if hasattr(snippet, 'audio') and snippet.audio:
                        logger.info(f"🎵 Audio data length: {len(snippet.audio)}")
                        # Decode base64 audio data to get raw audio bytes
                        audio_data = base64.b64decode(snippet.audio)
                        logger.info(f"🎵 Decoded audio data: {len(audio_data)} bytes")
                    else:
                        logger.warning(f"🎵 No audio data in snippet: {snippet}")
                        continue

                    # Extract generation ID if available
                    if hasattr(snippet, 'generation_id') and snippet.generation_id:
                        generation_id = snippet.generation_id

                    # Create chunk
                    chunk = TTSChunk(
                        audio_data=audio_data,
                        chunk_index=chunk_index,
                        is_final=False,  # We'll mark the last chunk as final
                        timestamp_ms=current_time * 1000,
                        generation_id=generation_id
                    )

                    logger.info(f"🎵 Created TTS chunk {chunk_index} with {len(audio_data)} bytes")
                    chunk_index += 1
                    yield chunk

                logger.info(f"🎵 Finished iterating over Hume API responses. Total snippets received: {snippet_count}")
            except Exception as iteration_error:
                logger.error(f"🎵 Error during Hume API iteration: {iteration_error}")
                import traceback
                logger.error(f"🎵 Traceback: {traceback.format_exc()}")
                raise
            
            # Send final chunk marker
            if chunk_index > 0:
                final_chunk = TTSChunk(
                    audio_data=b'',
                    chunk_index=chunk_index,
                    is_final=True,
                    timestamp_ms=time.time() * 1000,
                    generation_id=generation_id
                )
                yield final_chunk
            
            # Update performance metrics
            total_time = (time.time() - start_time) * 1000
            self._update_performance_metrics(total_time, first_chunk_time)
            
            logger.debug(f"TTS streaming completed in {total_time:.2f}ms")
            
        except Exception as e:
            logger.error(f"Error in TTS streaming: {e}")
            
            # Yield error chunk
            error_chunk = TTSChunk(
                audio_data=b'',
                chunk_index=0,
                is_final=True,
                timestamp_ms=time.time() * 1000,
                generation_id=None
            )
            yield error_chunk
    
    async def synthesize_complete(
        self,
        text: str,
        voice_name: Optional[str] = None,
        emotion_context: Optional[EmotionAnalysisResult] = None,
        acting_instructions: Optional[str] = None,
        speed: float = 1.0,
        context_generation_id: Optional[str] = None
    ) -> TTSResult:
        """
        Synthesize complete TTS audio (non-streaming).
        
        Args:
            text: Text to synthesize
            voice_name: Voice to use
            emotion_context: Emotion context for modulation
            acting_instructions: Acting instructions
            speed: Speech speed
            context_generation_id: Previous generation ID
            
        Returns:
            TTSResult with complete audio data
        """
        start_time = time.time()
        audio_chunks = []
        generation_id = None
        chunk_count = 0
        
        async for chunk in self.synthesize_streaming(
            text=text,
            voice_name=voice_name,
            emotion_context=emotion_context,
            acting_instructions=acting_instructions,
            speed=speed,
            context_generation_id=context_generation_id
        ):
            if chunk.audio_data:
                audio_chunks.append(chunk.audio_data)
            if chunk.generation_id:
                generation_id = chunk.generation_id
            chunk_count += 1
        
        # Combine all audio chunks
        complete_audio = b''.join(audio_chunks)
        processing_time = (time.time() - start_time) * 1000
        
        return TTSResult(
            audio_data=complete_audio,
            generation_id=generation_id or '',
            processing_time_ms=processing_time,
            voice_settings={
                'voice_name': voice_name,
                'speed': speed,
                'emotion_context': emotion_context.primary_emotion if emotion_context else None,
                'acting_instructions': acting_instructions
            },
            total_chunks=chunk_count
        )
    
    def _emotion_to_acting_instructions(self, emotion_context: EmotionAnalysisResult) -> str:
        """
        Convert emotion analysis result to acting instructions.
        
        Args:
            emotion_context: Emotion analysis result
            
        Returns:
            Acting instructions string
        """
        if not emotion_context or not emotion_context.emotions:
            return ""
        
        # Get top emotions
        top_emotions = sorted(emotion_context.emotions, key=lambda x: x.score, reverse=True)[:3]
        
        # Map emotions to acting instructions
        emotion_instructions = {
            'joy': 'cheerful, upbeat',
            'happiness': 'warm, pleasant',
            'excitement': 'energetic, animated',
            'love': 'tender, affectionate',
            'sadness': 'melancholy, gentle',
            'anger': 'firm, intense',
            'fear': 'cautious, concerned',
            'surprise': 'surprised, curious',
            'disgust': 'disapproving',
            'contempt': 'dismissive',
            'calm': 'serene, peaceful',
            'neutral': 'conversational'
        }
        
        instructions = []
        for emotion in top_emotions:
            emotion_name = emotion.name.lower()
            if emotion_name in emotion_instructions and emotion.score > 0.3:
                instructions.append(emotion_instructions[emotion_name])
        
        # Add intensity modifier
        if emotion_context.emotion_intensity > 0.7:
            instructions.append('with strong emotion')
        elif emotion_context.emotion_intensity > 0.5:
            instructions.append('with moderate emotion')
        
        # Add valence/arousal modifiers
        if emotion_context.emotion_valence < -0.5:
            instructions.append('subdued tone')
        elif emotion_context.emotion_valence > 0.5:
            instructions.append('positive tone')
        
        if emotion_context.emotion_arousal > 0.7:
            instructions.append('high energy')
        elif emotion_context.emotion_arousal < 0.3:
            instructions.append('calm delivery')
        
        return ', '.join(instructions[:4])  # Limit to 4 instructions
    
    def _update_performance_metrics(self, total_time_ms: float, first_chunk_time_ms: Optional[float]):
        """Update internal performance metrics."""
        self.request_count += 1
        self.total_processing_time += total_time_ms
        
        if first_chunk_time_ms:
            self.total_first_chunk_time += first_chunk_time_ms
    
    def get_performance_stats(self) -> Dict[str, float]:
        """Get performance statistics."""
        if self.request_count == 0:
            return {
                'average_processing_time_ms': 0.0,
                'average_first_chunk_time_ms': 0.0,
                'total_requests': 0
            }
        
        return {
            'average_processing_time_ms': self.total_processing_time / self.request_count,
            'average_first_chunk_time_ms': self.total_first_chunk_time / self.request_count,
            'total_requests': self.request_count
        }


# Global client instances for reuse
_emotion_client: Optional[HumeEmotionClient] = None
_tts_client: Optional[HumeTTSClient] = None


@asynccontextmanager
async def get_emotion_client():
    """Get shared emotion client instance."""
    global _emotion_client
    
    if _emotion_client is None:
        _emotion_client = HumeEmotionClient()
    
    try:
        yield _emotion_client
    finally:
        pass


@asynccontextmanager
async def get_tts_client():
    """Get shared TTS client instance."""
    global _tts_client
    
    if _tts_client is None:
        _tts_client = HumeTTSClient()
    
    try:
        yield _tts_client
    finally:
        pass


# Convenience functions
async def analyze_audio_emotions(
    audio_data: bytes,
    sample_rate: int = 16000
) -> EmotionAnalysisResult:
    """Convenience function for audio emotion analysis."""
    async with get_emotion_client() as client:
        return await client.analyze_audio_stream(audio_data, sample_rate)


async def analyze_text_emotions(text: str) -> EmotionAnalysisResult:
    """Convenience function for text emotion analysis."""
    async with get_emotion_client() as client:
        return await client.analyze_text_emotions(text)


def emotion_result_to_dict(emotion_result: EmotionAnalysisResult) -> Dict[str, Any]:
    """Convert EmotionAnalysisResult to dictionary format for compatibility."""
    if not emotion_result:
        return {}

    return {
        'primary_emotion': emotion_result.primary_emotion,
        'emotion_intensity': emotion_result.emotion_intensity,
        'emotion_valence': emotion_result.emotion_valence,
        'emotion_arousal': emotion_result.emotion_arousal,
        'emotions': [
            {
                'name': emotion.name,
                'score': emotion.score,
                'confidence': emotion.confidence
            }
            for emotion in emotion_result.emotions
        ],
        'confidence_score': emotion_result.confidence_score,
        'processing_time_ms': emotion_result.processing_time_ms,
        'source': emotion_result.source,
        'raw_data': emotion_result.raw_data
    }


def safe_get_emotion_context(emotion_context) -> Dict[str, Any]:
    """Safely convert emotion context to dictionary format."""
    if isinstance(emotion_context, EmotionAnalysisResult):
        return emotion_result_to_dict(emotion_context)
    elif isinstance(emotion_context, dict):
        return emotion_context
    else:
        return {}


async def synthesize_speech_streaming(
    text: str,
    voice_name: Optional[str] = None,
    emotion_context: Optional[EmotionAnalysisResult] = None,
    **kwargs
) -> AsyncGenerator[TTSChunk, None]:
    """Convenience function for streaming TTS."""
    async with get_tts_client() as client:
        async for chunk in client.synthesize_streaming(
            text=text,
            voice_name=voice_name,
            emotion_context=emotion_context,
            **kwargs
        ):
            yield chunk